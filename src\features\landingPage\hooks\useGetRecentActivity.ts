import { useGetRecentActivityQuery } from "@/api/recentActivityApiSlice";

export const useGetRecentActivity = () => {
    const { data: recentActivityResponse,
      isLoading: isLoadingRecentActivity,
      isFetching: isFetchingRecentActivity,
      error: recentActivityError,
      refetch: refetchRecentActivity,
    } = useGetRecentActivityQuery();
  

  return {
    recentActivityResponse,
    isLoadingRecentActivity,
    isFetchingRecentActivity,
    recentActivityError,
    refetchRecentActivity,
  };
};
