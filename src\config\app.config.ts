export type AppConfig = {
  env: string;
  logLevel: string;
  keycloakEnabled: boolean;
  applicationConfig: {
    appTimeZone: string;
    paginationConfig: {
      pageSizeOptions: number[];
      defaultSize: number;
    };
  };
  roles: Record<string, string>;

  featureFlags: {
    TERMS_API_USE_MOCK: boolean;
    RECENT_ACTIVITY_API_USE_MOCK: boolean;
    // future: USERS_API_USE_MOCK: boolean;
  };
};

const defaultAppConfig: AppConfig = {
  env: import.meta.env.MODE || "development",
  logLevel: import.meta.env.VITE_LOG_LEVEL || "info",
  keycloakEnabled: import.meta.env.VITE_KEYCLOAK_ENABLED === "true",
  applicationConfig: {
    appTimeZone: "UTC",
    paginationConfig: {
      pageSizeOptions: [10, 20, 50],
      defaultSize: 10,
    },
  },
  roles: {
    system_admin: "System Admin",
    admin: "Admin",
    manager: "Manager",
    officer: "Officer",
  },

  featureFlags: {
    TERMS_API_USE_MOCK: false,
    RECENT_ACTIVITY_API_USE_MOCK: true,
  },
};

export default defaultAppConfig;
