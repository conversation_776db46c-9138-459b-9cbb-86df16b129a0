import { Grid, GridColumn } from "@progress/kendo-react-grid";
import { Icon } from "@progress/kendo-react-common";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { useState, useMemo, type JSX } from "react";
import type { SortDescriptor } from "@progress/kendo-data-query";
import type { RecentActivity, SetupAreaType } from "@/types/recentActivity";
import { getSectionRoute } from "@/utils/sectionRouteMapper";
import "./RecentActivityTable.scss";
import { Cog6ToothIcon, FolderIcon, UserIcon } from "@heroicons/react/24/outline";

interface RecentActivityTableProps {
  data: RecentActivity[];
  isLoading?: boolean;
  onRefresh?: () => void;
}

const getSetupAreaIcon = (setupArea: SetupAreaType): React.ElementType => {
  switch (setupArea) {
    case "DMS Settings":
      return FolderIcon;
    case "Portal Settings":
      return Cog6ToothIcon;
    case "Shared Settings":
      return UserIcon;
    default:
      return Cog6ToothIcon;
  }
};

export default function RecentActivityTable({
  data,
  isLoading = false,
  onRefresh,
}: RecentActivityTableProps) {
  const { t } = useTranslation("dashboard");
  const navigate = useNavigate();

  const [sort, setSort] = useState<SortDescriptor[]>([]);

  // Handle refresh action
  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
    }
  };

  const handleSectionClick = (section: string) => {
    const route = getSectionRoute(section);
    if (route) {
      navigate(route);
    }
  };

  const sortedData = useMemo(() => {
    // If no manual sort is applied, default to sorting by lastUpdated (descending - most recent first)
    const sortToApply =
      sort.length === 0 ? [{ field: "lastUpdated", dir: "desc" }] : sort;

    return [...data].sort((a, b) => {
      for (const sortDesc of sortToApply) {
        const { field, dir } = sortDesc;
        if (!dir) continue;

        const aValue = a[field as keyof RecentActivity];
        const bValue = b[field as keyof RecentActivity];

        if (field === "lastUpdated") {
          const aDate = new Date(aValue);
          const bDate = new Date(bValue);

          const comparison = aDate.getTime() - bDate.getTime();
          if (comparison !== 0) {
            return dir === "asc" ? comparison : -comparison;
          }
        } else {
          const comparison = aValue.localeCompare(bValue);
          if (comparison !== 0) {
            return dir === "asc" ? comparison : -comparison;
          }
        }
      }
      return 0;
    });
  }, [data, sort]);

  const handleSortChange = (event: any) => {
    setSort(event.sort);
  };

  const SetupAreaCellIcon = (props: any) => {
    const { dataItem } = props;
    const MenuIcon = getSetupAreaIcon(dataItem.setupArea);

    return (
      <td>
        <div>
          <MenuIcon className="setup-area-icon" />
        </div>
      </td>
    );
  };

  const SectionCell = (props: any) => {
    const { dataItem } = props;
    const route = getSectionRoute(dataItem.section);

    return (
      <td className="section-cell">
        {route ? (
          <button
            type="button"
            className="section-link"
            onClick={() => handleSectionClick(dataItem.section)}
          >
            {dataItem.section}
          </button>
        ) : (
          <span>{dataItem.section}</span>
        )}
      </td>
    );
  };

  return (
    <div className="recent-activity-table">
      <div className="table-header">
        <h3 className="table-title">{t("recentActivity.title")}</h3>
      </div>
      <Grid
        data={sortedData}
        style={{ height: "400px" }}
        className="recent-activity-grid"
        sortable={true}
        sort={sort}
        autoProcessData={true}
        defaultSkip={0}
        defaultTake={10}
        pageable={{
          buttonCount: 4,
          pageSizes: [5, 10, 15],
        }}
        onSortChange={handleSortChange}
      >
        <GridColumn
          width="40px"
          cells={{
            data: SetupAreaCellIcon,
          }}
        />
        <GridColumn
          field="setupArea"
          title={t("recentActivity.columns.setupArea")}
        />
        <GridColumn
          field="section"
          title={t("recentActivity.columns.section")}
          cells={{
            data: SectionCell,
          }}
        />
        <GridColumn
          field="updatedBy"
          title={t("recentActivity.columns.updatedBy")}
        />
        <GridColumn
          field="lastUpdated"
          title={t("recentActivity.columns.lastUpdated")}
        />
      </Grid>

      <div className="grid-refresh-container">
        <button
          className="refresh-button"
          type="button"
          onClick={handleRefresh}
          title="Refresh data"
          disabled={isLoading}
        >
          <Icon name="refresh" />
        </button>
      </div>
    </div>
  );
}
