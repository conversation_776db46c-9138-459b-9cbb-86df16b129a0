import { useState } from "react";
import { useGetRecentActivity } from "./useGetRecentActivity";

export function useDashboard() {
  const [showAlert, setShowAlert] = useState(true);

  const {
    recentActivityResponse,
    isFetchingRecentActivity,
    isLoadingRecentActivity,
    recentActivityError,
    refetchRecentActivity,
  } = useGetRecentActivity();

  const recentActivityData = recentActivityResponse?.data || [];

  const handleRefreshRecentActivity = () => {
    refetchRecentActivity();
  };

  return {
    showAlert,
    setShowAlert,
    recentActivityData,
    isLoadingRecentActivity,
    isFetchingRecentActivity,
    recentActivityError,
    handleRefreshRecentActivity,
  };
}
