import { createApi } from "@reduxjs/toolkit/query/react";
import type {
  FetchBaseQueryError,
  QueryReturnValue,
} from "@reduxjs/toolkit/query";
import type { RecentActivityResponse } from "@/types/recentActivity";
import config from "@/config";
import { baseQueryWithReauth } from "./interceptorsSlice";
import { mockRecentActivityResponse } from "./mocks/recentActivity.mock";

export const recentActivityApiSlice = createApi({
  reducerPath: "recentActivityApi",
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    // GET RECENT ACTIVITY
    getRecentActivity: builder.query<RecentActivityResponse, void>({
      async queryFn(
        _arg,
        _queryApi,
        _extraOptions,
        baseQuery,
      ): Promise<QueryReturnValue<RecentActivityResponse, FetchBaseQueryError, {} | undefined>> {
        await new Promise((resolve) => setTimeout(resolve, 1500));

        if (config.featureFlags.RECENT_ACTIVITY_API_USE_MOCK) {
          return {
            data: mockRecentActivityResponse,
          };
        }

        const result = await baseQuery({
          url: "/recent-activity",
          method: "GET",
        });

        return result as QueryReturnValue<
          RecentActivityResponse,
          FetchBaseQueryError,
          {} | undefined
        >;
      },
    }),
  }),
});

export const { useGetRecentActivityQuery } = recentActivityApiSlice;
