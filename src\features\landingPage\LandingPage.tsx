import AlertBox from "@/components/ui/AlertBox/AlertBox";
import "./LandingPage.scss";
import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import { useTranslation } from "react-i18next";
import { useDashboard } from "./hooks/useDashboard";
import { RecentActivityTable } from "./components/RecentActivityTable";

export default function LandingPage() {
  const { t } = useTranslation("dashboard");

  const {
    showAlert,
    setShowAlert,
    recentActivityData,
    isLoadingRecentActivity,
    handleRefreshRecentActivity,
    isFetchingRecentActivity,
  } = useDashboard();

  return (
    <SectionLayout
      isLoading={isLoadingRecentActivity}
      isFetching={isFetchingRecentActivity}
    >
      <div className="landing-page-content">
        {showAlert && (
          <AlertBox
            message={t("landingPage.alertMessage")}
            onClose={() => setShowAlert(false)}
          />
        )}

        {recentActivityData.length && (
          <RecentActivityTable
            data={recentActivityData}
            isLoading={isLoadingRecentActivity}
            onRefresh={handleRefreshRecentActivity}
          />
        )}
      </div>
    </SectionLayout>
  );
}
