.recent-activity-table {
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .table-header {
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;

    .table-title {
      margin: 0;
    }
  }

  .recent-activity-grid {
    .k-grid-header {
      .k-header {
        padding: 12px 16px;
      }
    }

    .k-grid-content {
      .k-table-tbody {
        .k-table-row {

          .k-table-td {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
          }
        }
      }
    }
  }

  .setup-area-icon {
    color: #666;
  }

  .section-cell {
    .section-link {
      background: none;
      border: none;
      color: #007acc;
      cursor: pointer;
      font-size: 14px;
      padding: 0;

      &:hover {
        text-decoration: underline;
      }
    }

  }

  .grid-refresh-container {
    position: relative;
    margin-top: -40px;

    .refresh-button {
      position: absolute;
      right: 20px;
      top: 5px;
      background: none;
      border: none;
      cursor: pointer;
      padding: 6px;
      transition: background-color 0.2s ease;

      &:hover:not(:disabled) {
        background-color: rgba(0, 0, 0, 0.1);
      }

      .k-icon {
        color: #666;
      }
    }
  }

  .recent-activity-grid {
    .k-pager {
      position: relative;
      padding-right: 50px;
    }
  }
}
